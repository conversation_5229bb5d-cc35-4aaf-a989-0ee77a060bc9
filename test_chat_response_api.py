#!/usr/bin/env python3
"""
Test script to verify the chat response functionality works end-to-end.
This script tests:
1. Sending a chat message
2. Verifying response is saved to database
3. Retrieving chat history with response field
"""

import requests
import json
import time

# API base URL
BASE_URL = "http://localhost:8124"

def test_chat_and_response():
    """Test the complete chat and response flow."""
    print("🚀 Testing Chat Response API Flow")
    print("=" * 60)
    
    # Step 1: Send a chat message
    print("\n1️⃣ Sending chat message...")
    chat_payload = {
        "message": "Create a simple rectangular box with dimensions 10x20x30",
        "part_file_name": "test_box",
        "material_choice": "STEEL",
        "export_format": "obj",
        "is_edit_request": False
    }
    
    try:
        chat_response = requests.post(
            f"{BASE_URL}/api/chat_to_cad/",
            json=chat_payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if chat_response.status_code == 200:
            chat_data = chat_response.json()
            session_id = chat_data.get("session_id")
            print(f"✅ Chat request successful!")
            print(f"   Session ID: {session_id}")
            print(f"   Response: {chat_data.get('chat_response', 'No response')[:100]}...")
            
            if not session_id:
                print("❌ No session_id returned from chat API")
                return False
                
        else:
            print(f"❌ Chat request failed: {chat_response.status_code}")
            print(f"   Error: {chat_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error sending chat message: {e}")
        return False
    
    # Step 2: Wait a moment for database to be updated
    print("\n2️⃣ Waiting for database update...")
    time.sleep(2)
    
    # Step 3: Get chat history
    print("\n3️⃣ Retrieving chat history...")
    try:
        history_response = requests.get(
            f"{BASE_URL}/api/sessions/{session_id}/chat",
            params={"limit": 10},
            timeout=30
        )
        
        if history_response.status_code == 200:
            history_data = history_response.json()
            print(f"✅ Chat history retrieved successfully!")
            print(f"   Number of entries: {len(history_data)}")
            
            if history_data:
                latest_entry = history_data[0]  # Most recent entry
                print(f"\n📋 Latest Chat Entry:")
                print(f"   ID: {latest_entry.get('id')}")
                print(f"   Message: {latest_entry.get('message', 'No message')[:50]}...")
                print(f"   Response: {latest_entry.get('response', 'No response')[:50]}...")
                print(f"   Created At: {latest_entry.get('created_at')}")
                
                # Check if response field exists and has content
                if 'response' in latest_entry and latest_entry['response']:
                    print("✅ Response field exists and contains data!")
                    return True
                else:
                    print("❌ Response field is missing or empty!")
                    print(f"   Available fields: {list(latest_entry.keys())}")
                    return False
            else:
                print("❌ No chat history entries found!")
                return False
                
        else:
            print(f"❌ Failed to get chat history: {history_response.status_code}")
            print(f"   Error: {history_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error retrieving chat history: {e}")
        return False

def test_api_availability():
    """Test if the API is running."""
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API is running and accessible")
            return True
        else:
            print(f"❌ API returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API is not accessible: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing Chat Response API")
    print("=" * 60)
    
    # Check if API is running
    if not test_api_availability():
        print("\n❌ Please start the API server first:")
        print("   python run.py")
        exit(1)
    
    # Run the main test
    success = test_chat_and_response()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests passed! Chat response functionality is working correctly.")
        print("\nWhat was tested:")
        print("  ✓ Chat message processing")
        print("  ✓ Response saved to database")
        print("  ✓ Chat history API returns response field")
    else:
        print("❌ Tests failed! Please check the implementation.")
        print("\nTroubleshooting:")
        print("  1. Make sure the API server is running")
        print("  2. Check database connection")
        print("  3. Verify migration was applied correctly")
