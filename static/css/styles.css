@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
.typing-indicator {
  display: flex;
  padding: 8px 12px;
}
.typing-dot {
  width: 6px;
  height: 6px;
  margin: 0 2px;
  background-color: #6b7280;
  border-radius: 50%;
  animation: pulse 1.5s infinite ease-in-out;
}
.typing-dot:nth-child(1) {
  animation-delay: 0s;
}
.typing-dot:nth-child(2) {
  animation-delay: 0.3s;
}
.typing-dot:nth-child(3) {
  animation-delay: 0.6s;
}

#chatbot-widget {
  position: fixed;
  bottom: 30px;
  right: 20px;
  width: 400px;
  height: 500px;
  z-index: 1000;
  transition: all 0.3s ease;
  transform: translateY(20px);
  opacity: 0;
  visibility: hidden;
}

#chatbot-widget.active {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

#chat-toggle {
  position: fixed;
  bottom: 20px;
  right: 30px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #4f46e5;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  transition: opacity 0.3s ease;
}

#chat-toggle.hidden {
  opacity: 0;
  pointer-events: none;
}

.chat-messages {
  height: calc(100% - 110px);
}

.message {
  opacity: 0;
  transform: translateY(5px);
  animation: fadeIn 0.2s ease-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#edit-mode-indicator {
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(79, 70, 229, 0.1);
  transition: all 0.3s ease;
}

#edit-mode-indicator.hidden {
  display: none;
}

/* Chat history styles */
#chat-history {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

#chat-history::-webkit-scrollbar {
  width: 8px;
}

#chat-history::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 4px;
}

#chat-history::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 4px;
  border: 2px solid #f7fafc;
}

.use-code-btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.use-code-btn:hover {
  background-color: rgba(79, 70, 229, 0.1);
}

/* Fade-out animation for notifications */
@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  } /* Stay visible for 80% of the animation time */
  100% {
    opacity: 0;
  }
}

.fade-out {
  animation: fadeOut 3s forwards;
}

/* Bot message styling - updated with greater specificity */
html body #code-output div.message.bot {
  background-color: #e8f4ff !important;
  border-left: 3px solid #4299e1 !important;
  color: #2c5282 !important;
  padding: 8px 12px !important;
  border-radius: 8px !important;
  max-width: 90% !important;
  margin-bottom: 10px !important;
}

/* Additional style for bot messages regardless of container */
div.message.bot {
  background-color: #e8f4ff !important;
  border-left: 3px solid #4299e1 !important;
  color: #2c5282 !important;
}
