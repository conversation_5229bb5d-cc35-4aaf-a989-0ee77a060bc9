#!/usr/bin/env python3
"""
Debug script to check import issues.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🔍 Debugging import issues...")
print(f"Project root: {project_root}")
print(f"Python path: {sys.path[:3]}")

try:
    print("\n1️⃣ Testing basic src import...")
    import src
    print("✅ src import successful")
    
    print("\n2️⃣ Testing src.crud import...")
    import src.crud as crud
    print("✅ src.crud import successful")
    
    print("\n3️⃣ Checking available functions in crud...")
    crud_functions = [attr for attr in dir(crud) if not attr.startswith('_')]
    print(f"Available functions: {crud_functions}")
    
    print("\n4️⃣ Testing get_chats_by_session_id specifically...")
    if hasattr(crud, 'get_chats_by_session_id'):
        print("✅ get_chats_by_session_id is available")
        print(f"Function: {crud.get_chats_by_session_id}")
    else:
        print("❌ get_chats_by_session_id is NOT available")
        
    print("\n5️⃣ Testing direct import from sessions module...")
    from src.crud.sessions import get_chats_by_session_id
    print("✅ Direct import from sessions successful")
    print(f"Function: {get_chats_by_session_id}")
    
    print("\n6️⃣ Testing database connection...")
    from src.database.database import get_db
    db_gen = get_db()
    db = next(db_gen)
    print("✅ Database connection successful")
    
    print("\n7️⃣ Testing function call...")
    result = get_chats_by_session_id(db, "session_2277f9_601853", page=1, limit=5)
    print(f"✅ Function call successful, returned {len(result)} items")
    
    if result:
        first_item = result[0]
        print(f"First item type: {type(first_item)}")
        print(f"First item attributes: {[attr for attr in dir(first_item) if not attr.startswith('_')]}")
        if hasattr(first_item, 'response'):
            print(f"Response field exists: {first_item.response}")
        else:
            print("❌ Response field does not exist")
    
    db_gen.close()
    
except Exception as e:
    import traceback
    print(f"❌ Error: {e}")
    print(f"Traceback: {traceback.format_exc()}")

print("\n" + "="*50)
print("Debug completed!")
