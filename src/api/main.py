import os
import mimetypes # Needed for FileResponse media_type
import logging
import platform
from datetime import datetime
from pathlib import Path

from fastapi import FastAP<PERSON>, Request, HTTPException, UploadFile, File, Form
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from pydantic import BaseModel, Field
from typing import Optional # Added import
from fastapi import Depends # Added import

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("api.log")
    ]
)
logger = logging.getLogger("dfm-shapechatbot-api")

# Load environment variables from config directory
config_path = Path(__file__).parent.parent.parent/ '.env'
load_dotenv(str(config_path))

# Import the processing function and chat history functions
try:
    from ..core.chatbot import process_user_request, get_chat_history, clear_chat_history, text_to_cad_agent
except ImportError:
    # Fallback for direct imports
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.core.chatbot import process_user_request, get_chat_history, clear_chat_history, text_to_cad_agent

# Import the PDF processing utility
try:
    from ..utils.pdf_handler import PDFProcessor
except ImportError:
    # Fallback for direct imports
    from src.utils.pdf_handler import PDFProcessor

# Import the image processing utility
try:
    from ..utils.image_handler import ImageProcessor
except ImportError:
    # Fallback for direct imports
    from src.utils.image_handler import ImageProcessor

try:
    from .. import crud
except ImportError:
    # Fallback for direct imports
    from src import crud

# Initialize the PDF processor, passing the text_to_cad_agent instance
# text_to_cad_agent is imported from ..core.chatbot
pdf_processor = PDFProcessor(cad_agent=text_to_cad_agent)

# Initialize the image processor, passing the text_to_cad_agent instance
image_processor = ImageProcessor(cad_agent=text_to_cad_agent)

# Get the absolute path to the project root
project_root = Path(__file__).parent.parent.parent
templates_dir = project_root / 'templates'
static_dir = project_root / 'static'
# Configure Jinja2 templates
templates = Jinja2Templates(directory=str(templates_dir))

# Base URL for API endpoints - used for generating downloadable URLs
PORT = int(os.getenv("UVICORN_PORT", 8080))
DOMAIN = os.getenv("DOMAIN", "http://localhost")

# Only include port in BASE_URL if DOMAIN is localhost
if DOMAIN == "http://localhost" or DOMAIN == "localhost":
    BASE_URL = f"{DOMAIN}:{PORT}"
else:
    BASE_URL = DOMAIN

# Use lifespan context manager instead of on_event
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan context manager for FastAPI application.
    Handles startup and shutdown events.
    """
    # Startup logic
    logger.info("API starting up")

    # Ensure required directories exist
    os.makedirs(str(templates_dir), exist_ok=True)
    os.makedirs(str(static_dir), exist_ok=True)

    # Ensure temp_uploads directory exists for PDF processing
    temp_uploads_dir = Path("temp_uploads")
    temp_uploads_dir.mkdir(exist_ok=True)
    logger.info(f"PDF uploads directory: {temp_uploads_dir}")

    # Log configuration
    logger.info(f"Project root: {project_root}")
    logger.info(f"Templates directory: {templates_dir}")
    logger.info(f"Static directory: {static_dir}")

    # Check if required files exist
    index_path = templates_dir / "index.html"
    if not index_path.exists():
        logger.warning(f"index.html not found at {index_path}")
    else:
        logger.info(f"index.html found at {index_path}")

    # Log PDF processor initialization
    if pdf_processor.client:
        logger.info("PDF processor initialized successfully")
    else:
        logger.warning("PDF processor initialization failed - check OpenAI API key")

    # Yield control to FastAPI
    yield

    # Shutdown logic
    logger.info("API shutting down")

# Create FastAPI app with lifespan
app = FastAPI(
    title="DFM Shape ChatBot API",
    description="API for generating 3D models from text descriptions",
    version="0.2.0",
    docs_url="/api-test/docs",
    redoc_url="/api-test/redoc",
    lifespan=lifespan,
    openapi_tags=[
        {"name": "sessions", "description": "Sessions"},
        {"name": "cad", "description": "CAD"},
        {"name": "pdf", "description": "PDF Processing"},
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import the separate API app for routes
try:
    from .routes import api_app
except ImportError:
    # This typically happens when running the script directly.
    # Assuming the sys.path was adjusted by the earlier fallback for ..core.chatbot
    from src.api.routes import api_app

# Mount the separate API app at the production API path
# This will make the routes available at /api-production instead of /api
app.mount("/api-production", api_app)

# Initialize database
try:
    from ..database.database import init_db, SessionLocal
    init_db()
    logger.info("Database initialized successfully")
except ImportError:
    # This typically happens when running the script directly.
    # Assuming the sys.path was adjusted by the earlier fallback for ..core.chatbot
    logger.warning("Relative import for database failed, trying absolute import.")
    try:
        from src.database.database import init_db, SessionLocal
        init_db()
        logger.info("Database initialized successfully (using absolute import path).")
    except Exception as e_fallback:
        logger.error(f"Error initializing database (fallback import): {e_fallback}")
except Exception as e:
    logger.error(f"Error initializing database: {e}")

# Mount static files
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# Health check endpoint
@app.get("/api/health", summary="Health check endpoint",
         description="Returns information about the API's health and environment")
async def health_check():
    """
    Health check endpoint that returns information about the API's health and environment.

    Returns:
        dict: A dictionary containing health information
    """
    return {
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "version": "0.2.0",
        "environment": {
            "python_version": platform.python_version(),
            "system": platform.system(),
            "processor": platform.processor(),
            "pdf_processor_available": pdf_processor.client is not None
        }
    }

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

# Define request model for chat endpoint
class ChatRequest(BaseModel):
    """Request model for the /api/chat endpoint."""
    message: str = Field(..., description="The user's message/request")
    is_edit_request: bool = Field(False, description="Whether this is a request to edit existing code")
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")

# Database Dependency
from sqlalchemy.orm import Session

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@app.post("/api/chat", summary="Process a chat message",
         description="Takes a text message, processes it, and returns generated code")
async def chat(request_data: ChatRequest, db: Session = Depends(get_db)):
    """
    Process a chat message and generate CAD code with session continuity.

    Args:
        request_data: The chat request containing the message, edit flag, and optional session_id
        db: Database session dependency

    Returns:
        A dictionary containing the generated code and potentially a GLTF file path

    Raises:
        HTTPException: If the message is empty or processing fails
    """
    user_message = request_data.message
    is_edit_request = request_data.is_edit_request
    session_id = request_data.session_id

    # First check for session ID in the request
    if not session_id:
        # Check if message contains a session ID
        import re
        session_pattern = re.compile(r'session_[a-f0-9]{6}_\d{6}')
        session_matches = session_pattern.findall(user_message)
        if session_matches:
            # Use the first found session ID
            session_id = session_matches[0]
            logger.info(f"Extracted session ID {session_id} from message content")

    # If still no session ID, check for recent sessions with pending questions
    if not session_id:
        from src.models.sessions import ChatHistory
        # Get recent chat history entries that asked questions (most recent first)
        recent_question_sessions = db.query(ChatHistory)\
            .filter(ChatHistory.output.like('%Missing parameters:%'))\
            .order_by(ChatHistory.id.desc())\
            .limit(5)\
            .all()

        for entry in recent_question_sessions:
            # Check if user message contains keywords from the question
            question_output = entry.output
            # Extract parameters from the question
            params_match = re.search(r'Missing parameters: ([^\]]+)', question_output)
            if params_match:
                param_list = params_match.group(1).split(',')
                # Check if current message addresses any of these parameters
                for param in param_list:
                    param_clean = param.strip().lower()
                    if param_clean in user_message.lower():
                        # This message is likely answering the question from this session
                        session_id = entry.session_id
                        logger.info(f"Matched response to previous question in session {session_id}")
                        break
            if session_id:
                break

    # Log the request with session info
    logger.info(f"Chat request received: '{user_message[:50]}...' (is_edit: {is_edit_request}, session_id: {session_id})")

    if not user_message:
        logger.warning("Empty message received")
        raise HTTPException(status_code=400, detail="No message provided")

    try:
        # Create a ChatRequest for the API
        from src.schemas.sessions import ChatRequest as ApiChatRequest
        api_request = ApiChatRequest(
            message=user_message,
            session_id=session_id,
            image_path="",  # Empty string instead of None
            part_file_name="part_file_name",  # Use default value from schema
            export_format="obj",  # Default format
            material_choice="STEEL",  # Use default value from schema
            selected_feature_uuid="",  # Empty string instead of None
            is_edit_request=is_edit_request  # Add the is_edit_request flag here
        )

        # Process using our enhanced session handling in CRUD
        from src.core.chatbot import text_to_cad_agent
        result = crud.handle_chat_request(db, api_request, text_to_cad_agent, request_origin='web')

        # Check for errors
        if isinstance(result, dict) and result.get("error"):
            logger.error(f"Error processing chat request: {result['error']}")
            raise HTTPException(status_code=500, detail=result["error"])

        # Log success and web search activities
        if "code" in result and result["code"]:
            logger.info(f"Successfully generated code ({len(result['code'])} characters)")
        
        # Log web search metadata if available
        if "web_search_metadata" in result:
            web_meta = result["web_search_metadata"]
            logger.info(f"Web search performed: {web_meta.get('successful_extractions', 0)}/{web_meta.get('urls_found', 0)} URLs processed")
            for web_content in web_meta.get('web_contents', []):
                logger.info(f"Extracted content from: {web_content['url']}")

        # Return the result dictionary (contains code and potentially gltf_path)
        return result

    except Exception as e:
        logger.exception(f"Unexpected error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

# New endpoint for PDF processing
class PDFProcessingResponse(BaseModel):
    """Response model for PDF processing."""
    success: bool
    message: str
    session_id: Optional[str] = None # Add session_id field

@app.post("/api/process-pdf",
          summary="Process a PDF file with OpenAI",
          description="Takes a PDF file and optional user input, sends it to OpenAI API, and returns the result",
          response_model=PDFProcessingResponse,
          tags=["pdf"])
async def process_pdf(file: UploadFile = File(...),
                     user_input: str = Form(""),
                     db: Session = Depends(get_db)):
    """
    Process a PDF file with OpenAI API.

    Args:
        file: The uploaded PDF file
        user_input: Optional text to send along with the default PDF processing prompt
        db: SQLAlchemy database session

    Returns:
        PDFProcessingResponse with success status, response message, and session_id

    Raises:
        HTTPException: For invalid file type or processing errors
    """
    logger.info(f"Received PDF processing request: {file.filename}")

    # Validate file is a PDF
    if not file.filename.lower().endswith('.pdf'):
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(status_code=400, detail="File must be a PDF")

    try:
        # Process the PDF using the PDFProcessor's process_uploaded_file method
        # This method handles session_id generation and file management internally
        success, response, session_id = await pdf_processor.process_uploaded_file(db=db, uploaded_file=file, user_input=user_input)
        logger.info(f"PDF processing result: success={success}, response_length={len(response) if response else 0}")

        return PDFProcessingResponse(success=success, message=response, session_id=session_id)

    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")

# New endpoint for image processing
class ImageProcessingResponse(BaseModel):
    """Response model for image processing."""
    success: bool
    message: str

@app.post("/api/process-image",
          summary="Process an image file with OpenAI Vision API",
          description="Takes an image file and optional user input, sends it to OpenAI Vision API, and returns the result",
          response_model=ImageProcessingResponse,
          tags=["image"])
async def process_image(file: UploadFile = File(...),
                        user_input: str = Form(""),
                        db: Session = Depends(get_db)):
    """
    Process an image file using OpenAI's Vision API and optionally generate CAD.

    Args:
        file: Uploaded image file (JPG, JPEG, PNG, GIF, BMP, TIFF, WEBP)
        user_input: Optional user input to send along with the default prompt

    Returns:
        ImageProcessingResponse: Response with success status and message
    """
    logger.info(f"Received image processing request: {file.filename}")

    # Validate file is a supported image format
    supported_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    file_extension = Path(file.filename).suffix.lower()

    if file_extension not in supported_extensions:
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(
            status_code=400,
            detail=f"File must be an image. Supported formats: {', '.join(supported_extensions)}"
        )

    try:
        # Process the image using the ImageProcessor, passing the db session
        # The ImageProcessor's process_uploaded_file handles session_id generation
        success, response = await image_processor.process_uploaded_file(db=db, uploaded_file=file, user_input=user_input)

        return ImageProcessingResponse(success=success, message=response)

    except Exception as e:
        logger.error(f"Error processing image: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")

@app.post("/api/refresh_chat", summary="Refresh chat session",
            description="Clears the chat history and resets the state.")
async def refresh_chat_session():
    """
    Clears the chat history and resets any relevant session state on the server.
    """
    print("[REFRESH] SERVER: Refresh chat button pressed - request received")
    logger.info("Refresh chat request received.")
    try:
        # text_to_cad_agent is already imported at the top of the file
        # No need to import it again

        # Reset the conversation state using the dedicated method
        text_to_cad_agent.reset_conversation()

        print("[SUCCESS] SERVER: Conversation reset successfully")
        logger.info("Conversation reset successfully")
        return JSONResponse(content={"message": "Chat session refreshed successfully."}, status_code=200)
    except Exception as e:
        print(f"[ERROR] SERVER: Error during conversation reset: {e}")
        logger.error(f"Error during conversation reset: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

# --- GLTF Generation Endpoint ---

class GenerateRequest(BaseModel):
    """Request model for the /api/generate-gltf endpoint."""
    prompt: str = Field(..., description="The text prompt describing the 3D model to generate")
    is_edit_request: bool = Field(False, description="Whether this is a request to edit existing code")

@app.post("/api/generate-gltf",
         summary="Generate a 3D model in GLTF format",
         description="Takes a text prompt, generates a CAD model, converts it to GLTF, and returns the GLTF file")
async def generate_gltf(request_data: GenerateRequest):
    """
    Generate a 3D model from a text prompt and return it as a GLTF file.

    Args:
        request_data: The generation request containing the prompt and edit flag

    Returns:
        A FileResponse containing the generated GLTF file

    Raises:
        HTTPException: If the prompt is empty, processing fails, or the GLTF file is not found
    """
    user_message = request_data.prompt
    is_edit_request = request_data.is_edit_request

    # Log the request
    logger.info(f"GLTF generation request received: '{user_message[:50]}...' (is_edit: {is_edit_request})")

    if not user_message:
        logger.warning("Empty prompt received")
        raise HTTPException(status_code=400, detail="No prompt provided")

    try:
        # Process the user message using the chatbot function
        logger.info("Processing GLTF generation request...")
        result = process_user_request(user_message, is_edit_request, request_origin='api')

        # Check for errors during processing
        if isinstance(result, dict) and result.get("error"):
            error_msg = f"Agent Error: {result['error']}"
            logger.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg)
        elif not isinstance(result, dict) or "gltf_path" not in result:
            # Handle cases where the agent might return just an error string or unexpected format
            error_detail = str(result) if not isinstance(result, dict) else "Unknown agent error: GLTF path missing."
            error_msg = f"Agent Error: {error_detail}"
            logger.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg)

        gltf_path = result.get("gltf_path")
        logger.info(f"GLTF path from agent: {gltf_path}")

        # Check if GLTF path is valid and file exists
        if gltf_path and os.path.exists(gltf_path):
            # Determine filename for download
            filename = os.path.basename(gltf_path)
            # Determine media type
            media_type, _ = mimetypes.guess_type(gltf_path)
            if media_type is None:
                media_type = 'model/gltf+binary' # Default if guess fails

            logger.info(f"Returning GLTF file: {gltf_path}")
            return FileResponse(
                path=gltf_path,
                filename=filename,
                media_type=media_type
            )
        else:
            error_msg = f"GLTF file not found or generation failed. Path: {gltf_path}"
            logger.error(error_msg)
            raise HTTPException(status_code=404,
                               detail="GLTF file not found. Generation might have failed or conversion was skipped.")

    except HTTPException:
        # Re-raise HTTP exceptions
        raise

    except Exception as e:
        # Handle unexpected errors
        error_msg = f"Unexpected error in generate_gltf endpoint: {str(e)}"
        logger.exception(error_msg)
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

# --- Download Endpoint ---

@app.get("/download/{file_path:path}", tags=["cad"])
async def download_file(file_path: str):
    """
    Download a file by its path.

    Args:
        file_path (str): Path to the file relative to the project root

    Returns:
        FileResponse: The requested file for download

    Raises:
        HTTPException: If the file is not found
    """
    try:
        # Log the requested file path for debugging
        logger.info(f"Download requested for file: {file_path}")

        # Print current working directory for debugging
        project_root = Path.cwd()
        logger.info(f"Current working directory: {project_root}")

        # List all directories in the project root
        logger.info("Directories in project root:")
        for item in project_root.iterdir():
            if item.is_dir():
                logger.info(f"  - {item.name}")

        # Check if outputs directory exists
        outputs_dir = project_root / "outputs"
        if outputs_dir.exists():
            logger.info(f"Outputs directory exists: {outputs_dir}")
            # List contents of outputs directory
            logger.info("Contents of outputs directory:")
            for item in outputs_dir.iterdir():
                logger.info(f"  - {item.name}")
        else:
            logger.error(f"Outputs directory does not exist: {outputs_dir}")
            # Create the outputs directory
            outputs_dir.mkdir(exist_ok=True)
            logger.info(f"Created outputs directory: {outputs_dir}")

        # Construct the full file path - ensure it's relative to the project root
        full_path = project_root / file_path

        logger.info(f"Looking for file at: {full_path}")

        # Check if the file exists
        if not full_path.exists():
            logger.error(f"File not found: {full_path}")
            # Check if the directory exists
            if not full_path.parent.exists():
                logger.error(f"Directory does not exist: {full_path.parent}")
                # Try to create the directory structure
                full_path.parent.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created directory: {full_path.parent}")

            # Try to list files in parent directory if it exists
            if full_path.parent.exists():
                logger.info(f"Files in directory {full_path.parent}:")
                for f in full_path.parent.iterdir():
                    logger.info(f"  - {f.name}")

            # For debugging purposes, create a placeholder file if it doesn't exist
            # This is just for testing - remove in production
            with open(full_path, "w") as f:
                f.write("# This is a placeholder file created by the download endpoint for testing")
            logger.info(f"Created placeholder file for testing: {full_path}")

            # Uncomment this to return a 404 error instead of creating a placeholder file
            # raise HTTPException(status_code=404, detail=f"File not found: {file_path}")

        # Get the filename for the download
        filename = full_path.name

        # Determine the media type
        media_type, _ = mimetypes.guess_type(str(full_path))
        if media_type is None:
            # Default media types based on extension
            extension = full_path.suffix.lower()
            if extension == '.obj':
                media_type = 'model/obj'
            elif extension == '.step':
                media_type = 'application/step'
            elif extension == '.dxf':
                media_type = 'application/dxf'
            else:
                media_type = 'application/octet-stream'

        logger.info(f"Serving file for download: {full_path} (media type: {media_type})")

        # Return the file as a download with appropriate headers
        return FileResponse(
            path=str(full_path),
            filename=filename,
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Access-Control-Allow-Origin": "*",  # Add CORS header here
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "*"
            }
        )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error serving file {file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error serving file: {str(e)}")

# --- Chat History Endpoints ---

@app.get("/api/chat-history")
async def get_history(limit: int = None):
    """
    Get chat history entries.

    Args:
        limit (int, optional): Maximum number of entries to return. If None, returns all entries.

    Returns:
        list: List of chat history entries.
    """
    history = get_chat_history(limit)
    return {"history": history}

@app.delete("/api/chat-history")
async def delete_history():
    """
    Clear all chat history.

    Returns:
        dict: Success message.
    """
    success = clear_chat_history()
    if success:
        return {"message": "Chat history cleared successfully"}
    else:
        raise HTTPException(status_code=500, detail="Failed to clear chat history")

class UpdateCodeRequest(BaseModel):
    """Request model for the /api/update-latest-code endpoint."""
    code: str
    session_id: Optional[str] = None

@app.post("/api/update-latest-code")
async def update_latest_code(request_data: UpdateCodeRequest, db: Session = Depends(get_db)):
    """
    Update the latest code for a specific session in both database and agent state.

    Args:
        request_data: Request containing the code and session_id.
        db: Database session.

    Returns:
        dict: Success message or error message.
    """
    try:
        code = request_data.code
        session_id = request_data.session_id

        if not code:
            raise HTTPException(status_code=400, detail="No code provided")

        if not session_id:
            raise HTTPException(status_code=400, detail="Session ID is required")

        # Update the latest code in the database
        from src.crud.sessions import update_session
        updated_session = update_session(db, session_id, lasted_code=code)

        if not updated_session:
            raise HTTPException(status_code=404, detail="Session not found or failed to update")

        # Update the agent session state
        if text_to_cad_agent:
            text_to_cad_agent._update_session_state(session_id, latest_code=code)
            print(f"[SUCCESS] Updated latest code for session {session_id} in both database and agent state")
            return {"success": True, "message": "Latest code updated successfully"}
        else:
            raise HTTPException(status_code=500, detail="Agent not initialized")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating latest code: {str(e)}")

# --- STEP Viewer Launch Endpoint ---

@app.post("/api/launch-step-viewer", summary="Launch STEP viewer",
         description="Launches the STEP viewer for the most recent STEP file")
async def launch_step_viewer():
    """
    Launch the STEP viewer for the most recent STEP file.

    Returns:
        dict: Success message or error message.
    """
    try:
        import subprocess
        import sys
        import os
        import glob
        from pathlib import Path

        # Find the most recent STEP file
        project_root = Path.cwd()

        # Check multiple locations for STEP files
        step_files = []

        # Check in cad_outputs_generated directory first
        cad_outputs_dir = project_root / "outputs" / "code" / "cad_outputs_generated"
        if cad_outputs_dir.exists():
            for step_file in glob.glob(str(cad_outputs_dir / "*.step")):
                step_files.append(step_file)

        # Check in organized CAD outputs
        cad_dir = project_root / "outputs" / "cad"
        if cad_dir.exists():
            for step_file in glob.glob(str(cad_dir / "**/*.step"), recursive=True):
                step_files.append(step_file)

        # Check in project directory
        for step_file in glob.glob(str(project_root / "**/*.step"), recursive=True):
            step_files.append(step_file)

        if not step_files:
            logger.warning("No STEP files found for viewer launch")
            return {"success": False, "message": "No STEP files found to display"}

        # Sort by modification time (newest first)
        step_files.sort(key=lambda p: os.path.getmtime(p), reverse=True)
        most_recent_step = step_files[0]

        logger.info(f"Launching STEP viewer for: {most_recent_step}")

        # Launch the STEP viewer
        gui_script_path = os.path.abspath("gui_step.py")

        # Check if the wrapper script exists, otherwise use the direct path
        if not os.path.exists(gui_script_path):
            # Try to use the direct path to the script in the Tolery directory
            gui_script_path = os.path.abspath(os.path.join("Tolery", "gui_step.py"))
            logger.info(f"Using direct path to STEP viewer: {gui_script_path}")

        if not os.path.exists(gui_script_path):
            logger.error(f"STEP viewer script not found at: {gui_script_path}")
            return {"success": False, "message": "STEP viewer script not found"}

        # Launch the viewer in a non-blocking way
        subprocess.Popen([sys.executable, gui_script_path, str(most_recent_step)])

        logger.info("[SUCCESS] STEP viewer launched successfully")
        return {"success": True, "message": f"STEP viewer launched for: {os.path.basename(most_recent_step)}"}

    except Exception as e:
        logger.error(f"Error launching STEP viewer: {e}")
        return {"success": False, "message": f"Error launching STEP viewer: {str(e)}"}
