#!/usr/bin/env python3
"""
Migration script to add 'response' column to chat_history table.
This script adds a new TEXT column to store chatbot responses.
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from dotenv import load_dotenv
from urllib.parse import quote_plus

# Load environment variables
load_dotenv()

def get_database_url():
    """Get database URL from environment variables."""
    db_host = os.getenv("MYSQL_HOST", "**************")
    db_port = os.getenv("MYSQL_PORT", "33061")
    db_user = os.getenv("MYSQL_USER", "root")
    # Try to get password from MYSQL_ROOT_PASSWORD first, then fall back to MYSQL_PASSWORD
    db_password = os.getenv("MYSQL_ROOT_PASSWORD", os.getenv("MYSQL_PASSWORD", "root@H123"))
    db_name = os.getenv("MYSQL_DATABASE", "atn_tolery")

    # URL encode the password to handle special characters
    db_password_encoded = quote_plus(db_password)

    print(f"Database config:")
    print(f"  Host: {db_host}")
    print(f"  Port: {db_port}")
    print(f"  User: {db_user}")
    print(f"  Database: {db_name}")

    return f"mysql+pymysql://{db_user}:{db_password_encoded}@{db_host}:{db_port}/{db_name}"

def add_response_column():
    """Add response column to chat_history table."""
    database_url = get_database_url()

    try:
        # Create engine
        engine = create_engine(database_url)

        # Check if column already exists
        check_column_query = text("""
            SELECT COUNT(*) as column_exists
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = :db_name
            AND TABLE_NAME = 'chat_history'
            AND COLUMN_NAME = 'response'
        """)

        with engine.connect() as connection:
            result = connection.execute(check_column_query, {"db_name": os.getenv("MYSQL_DATABASE", "atn_tolery")})
            column_exists = result.fetchone()[0]

            if column_exists > 0:
                print("✅ Column 'response' already exists in chat_history table.")
                return True

            # Add the response column
            add_column_query = text("""
                ALTER TABLE chat_history
                ADD COLUMN response TEXT NULL
                COMMENT 'Store chat response from the chatbot'
            """)

            print("🔄 Adding 'response' column to chat_history table...")
            connection.execute(add_column_query)
            connection.commit()
            print("✅ Successfully added 'response' column to chat_history table.")

            return True

    except SQLAlchemyError as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main function to run the migration."""
    print("🚀 Starting migration: Add response column to chat_history table")
    print("-" * 60)

    success = add_response_column()

    print("-" * 60)
    if success:
        print("✅ Migration completed successfully!")
        print("\nNext steps:")
        print("1. Restart your application")
        print("2. Test the get_chat_history API to verify response field is included")
        print("3. Send a new chat message to verify response is saved to database")
    else:
        print("❌ Migration failed!")
        print("Please check the error messages above and try again.")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
