<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Text-to-CAD Chatbot</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="/static/css/styles.css" />
    <style>
      /* Emergency inline style for bot messages */
      #code-output .message.bot {
        background-color: #e8f4ff !important;
        border-left: 3px solid #4299e1 !important;
        color: #2c5282 !important;
        padding: 8px 12px !important;
        border-radius: 8px !important;
        max-width: 90% !important;
        margin-bottom: 10px !important;
      }

      /* Parameter message specific styles */
      #code-output .parameter-message {
        background-color: #e8f4ff !important;
        border-left: 3px solid #4299e1 !important;
        color: #2c5282 !important;
      }

      /* General bot message styles */
      .message.bot {
        background-color: #e8f4ff !important;
        border-left: 3px solid #4299e1 !important;
        color: #2c5282 !important;
      }

      /* Override for the gray background seen in the screenshot */
      #code-output pre {
        background-color: #e8f4ff !important;
        border-left: 3px solid #4299e1 !important;
        color: #2c5282 !important;
        padding: 8px 12px !important;
        border-radius: 8px !important;
        max-width: 100% !important;
        margin-bottom: 10px !important;
      }

      /* Loading states */
      .btn-loading {
        position: relative;
        color: transparent !important;
      }

      .btn-loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-left: -8px;
        margin-top: -8px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: button-spin 0.8s linear infinite;
      }

      @keyframes button-spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* Form processing overlay */
      .form-processing::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(1px);
        border-radius: 12px;
        z-index: 10;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <!-- Main website content -->
    <div class="container mx-auto px-4 py-12">
      <h1 class="text-3xl font-bold text-gray-800 mb-6">
        Text-to-CAD Generator
      </h1>
      <!-- Enhanced CAD Description Input -->
      <div class="mb-6">
        <!-- Header Controls -->
        <div class="flex justify-between items-center mb-3">
          <div class="flex items-center space-x-4">
            <h2 class="text-lg font-semibold text-gray-800">
              Generate CAD Model
            </h2>
            <label class="inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                id="edit-mode-toggle"
                class="sr-only peer"
              />
              <div
                class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"
              ></div>
              <span class="ml-2 text-sm text-gray-600">Edit Mode</span>
            </label>
          </div>

          <div class="flex items-center space-x-3">
            <div
              id="session-indicator"
              class="hidden text-xs text-gray-500 font-mono bg-gray-100 px-2 py-1 rounded"
            >
              <i class="fas fa-link mr-1"></i>
              <span id="session-id-display"></span>
            </div>
            <div
              id="edit-mode-indicator"
              class="hidden text-sm text-indigo-600 font-medium"
            >
              <i class="fas fa-pencil-alt mr-1"></i> Editing Code
            </div>
          </div>
        </div>

        <!-- Enhanced Input Form -->
        <div
          class="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden"
        >
          <!-- File Upload Section -->
          <div
            id="selected-file-info"
            class="empty:hidden border-b border-gray-100 p-3 bg-gray-50"
          ></div>

          <!-- Main Input Area -->
          <form id="chat-form" class="relative">
            <div class="flex items-end">
              <!-- Textarea Container -->
              <div class="flex-1 relative">
                <textarea
                  id="user-input"
                  placeholder="💬 Describe the CAD model you want to create in detail..."
                  class="w-full p-4 text-sm border-0 focus:outline-none resize-none bg-transparent min-h-[80px] max-h-[200px]"
                  rows="3"
                ></textarea>

                <!-- Paste Hint Overlay -->
              </div>

              <!-- Action Buttons -->
              <div
                class="flex items-center space-x-2 p-3 border-l border-gray-100"
              >
                <button
                  type="button"
                  class="w-10 h-10 rounded-lg bg-gray-100 text-gray-600 flex items-center justify-center hover:bg-gray-200 transition-colors"
                  id="attach-file-btn"
                  title="Attach file (PDF/Image)"
                >
                  <i class="fas fa-paperclip text-sm"></i>
                </button>

                <button
                  type="button"
                  class="w-10 h-10 rounded-lg bg-gray-100 text-gray-600 flex items-center justify-center hover:bg-gray-200 transition-colors"
                  id="refresh-btn"
                  title="Refresh chat"
                >
                  <i class="fas fa-sync-alt text-sm"></i>
                </button>

                <button
                  type="submit"
                  class="w-10 h-10 rounded-lg bg-indigo-600 text-white flex items-center justify-center hover:bg-indigo-700 transition-colors disabled:bg-gray-300"
                  id="send-btn"
                  disabled
                >
                  <i class="fas fa-paper-plane text-sm"></i>
                </button>
              </div>
            </div>

            <!-- Input Methods Info -->
            <div
              class="px-4 pb-3 flex items-center justify-between text-xs text-gray-500"
            >
              <div class="flex items-center space-x-4">
                <span class="flex items-center">
                  <i class="fas fa-keyboard mr-1"></i>
                  Type description
                </span>
                <span class="flex items-center">
                  <i class="fas fa-paste mr-1"></i>
                  Ctrl+V to paste image
                </span>
                <span class="flex items-center">
                  <i class="fas fa-mouse mr-1"></i>
                  Drag & drop file
                </span>
              </div>

              <div class="flex items-center space-x-2">
                <span id="char-count" class="text-gray-400">0</span>
                <span class="text-gray-300">|</span>
                <span class="text-gray-400"
                  >Supports: JPG, PNG, PDF (max 20MB)</span
                >
              </div>
            </div>
          </form>
        </div>
      </div>
      <!-- Enhanced Results Section -->
      <div
        class="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden"
      >
        <div
          class="flex justify-between items-center p-6 border-b border-gray-100 bg-gray-50"
        >
          <div class="flex items-center space-x-3">
            <div
              class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center"
            >
              <i class="fas fa-code text-indigo-600 text-sm"></i>
            </div>
            <div>
              <h2 class="text-lg font-semibold text-gray-900">
                Model Generation Result
              </h2>
              <p class="text-sm text-gray-600">FreeCAD Code & 3D Preview</p>
            </div>
          </div>

          <button
            type="button"
            class="hidden px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors shadow-sm"
            id="view-step-btn"
            title="View 3D Model"
          >
            <i class="fas fa-cube mr-2"></i>View 3D Model
          </button>
        </div>

        <div class="p-6">
          <div
            id="code-output"
            class="h-96 bg-gray-50 rounded-lg p-4 overflow-y-auto font-mono text-sm text-gray-800 border border-gray-200"
          >
            <!-- Generated code will be displayed here -->
            <div
              class="flex flex-col items-center justify-center h-full text-gray-400"
            >
              <i class="fas fa-robot text-4xl mb-4 text-gray-300"></i>
              <p class="text-center">
                Enter a description or upload an image to start generating CAD
                models
              </p>
              <p class="text-xs text-gray-400 mt-2">
                Supports: Text description, images (JPG, PNG), PDF files
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Chatbot toggle button -->
      <div id="chat-toggle">
        <i class="fas fa-robot text-2xl"></i>
      </div>

      <!-- Chatbot widget -->
      <div
        id="chatbot-widget"
        class="bg-white rounded-lg shadow-lg overflow-hidden flex flex-col"
      >
        <!-- Chat header -->
        <div
          class="bg-indigo-600 text-white px-4 py-3 flex items-center justify-between"
        >
          <div class="flex items-center space-x-2">
            <i class="fas fa-robot"></i>
            <span class="font-medium">Text-to-CAD</span>
          </div>
          <div class="flex items-center space-x-3">
            <!-- Removed model selector -->
            <!-- Removed new chat button -->
            <button
              class="text-white hover:text-indigo-200 transition"
              id="minimize-chat"
            >
              <i class="fas fa-minus"></i>
            </button>
            <button
              class="text-white hover:text-indigo-200 transition"
              id="close-chat"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- Removed chat messages section -->

        <!-- Removed saved shapes section -->
      </div>
    </div>

    <script src="/static/js/main.js"></script>
  </body>
</html>
