<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Text-to-CAD Chatbot</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="/static/css/styles.css" />
    <style>
      /* Emergency inline style for bot messages */
      #code-output .message.bot {
        background-color: #e8f4ff !important;
        border-left: 3px solid #4299e1 !important;
        color: #2c5282 !important;
        padding: 8px 12px !important;
        border-radius: 8px !important;
        max-width: 90% !important;
        margin-bottom: 10px !important;
      }

      /* Parameter message specific styles */
      #code-output .parameter-message {
        background-color: #e8f4ff !important;
        border-left: 3px solid #4299e1 !important;
        color: #2c5282 !important;
      }

      /* General bot message styles */
      .message.bot {
        background-color: #e8f4ff !important;
        border-left: 3px solid #4299e1 !important;
        color: #2c5282 !important;
      }

      /* Override for the gray background seen in the screenshot */
      #code-output pre {
        background-color: #e8f4ff !important;
        border-left: 3px solid #4299e1 !important;
        color: #2c5282 !important;
        padding: 8px 12px !important;
        border-radius: 8px !important;
        max-width: 100% !important;
        margin-bottom: 10px !important;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <!-- Main website content -->
    <div class="container mx-auto px-4 py-12">
      <h1 class="text-3xl font-bold text-gray-800 mb-6">
        Text-to-CAD Generator
      </h1>
      <!-- CAD Description Input -->
      <div class="mb-6">
        <div class="flex justify-between items-center mb-2">
          <div class="text-sm text-gray-600">
            <label class="inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                id="edit-mode-toggle"
                class="sr-only peer"
              />
              <div
                class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"
              ></div>
              <span class="ml-2">Edit Mode</span>
            </label>
          </div>
          <div class="flex items-center space-x-3">
            <div
              id="session-indicator"
              class="hidden text-xs text-gray-500 font-mono bg-gray-100 px-2 py-1 rounded"
            >
              <i class="fas fa-link mr-1"></i>
              <span id="session-id-display"></span>
            </div>
            <div
              id="edit-mode-indicator"
              class="hidden text-sm text-indigo-600 font-medium"
            >
              <i class="fas fa-pencil-alt mr-1"></i> Editing Previous Code
            </div>
          </div>
        </div>
        <form id="chat-form" class="flex items-center space-x-2">
          <!-- New wrapper for textarea and file info -->
          <div
            class="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus-within:ring-1 focus-within:ring-indigo-500 bg-white flex flex-col"
          >
            <div
              id="selected-file-info"
              class="text-sm text-gray-600 mb-1 empty:mb-0"
            ></div>
            <!-- Moved and styled for inline display -->
            <textarea
              id="user-input"
              placeholder="Enter CAD description..."
              class="w-full focus:outline-none resize-y text-sm"
              rows="2"
            ></textarea>
            <!-- Adjusted rows and removed some classes -->
          </div>
          <button
            type="submit"
            class="w-9 h-9 rounded-full bg-indigo-600 text-white flex items-center justify-center hover:bg-indigo-700 transition"
            id="send-btn"
          >
            <i class="fas fa-paper-plane text-sm"></i>
          </button>
          <button
            type="button"
            class="w-9 h-9 rounded-full bg-gray-200 text-gray-700 flex items-center justify-center hover:bg-gray-300 transition"
            id="refresh-btn"
            title="Refresh Chat"
          >
            <i class="fas fa-sync-alt text-sm"></i>
          </button>
          <button
            type="button"
            class="w-9 h-9 rounded-full bg-gray-200 text-gray-700 flex items-center justify-center hover:bg-gray-300 transition"
            id="attach-file-btn"
            title="Attach File (PDF/Image)"
          >
            <i class="fas fa-paperclip text-sm"></i>
          </button>
        </form>
        <!-- selected-file-info is now inside the form structure -->
      </div>
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold">Generated FreeCAD Code</h2>
          <button
            type="button"
            class="hidden px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
            id="view-step-btn"
            title="View STEP File"
          >
            <i class="fas fa-cube mr-2"></i>View 3D Model
          </button>
        </div>
        <div
          id="code-output"
          class="h-96 bg-gray-100 rounded p-4 overflow-y-auto font-mono text-sm text-gray-800"
        >
          <!-- Generated code will be displayed here -->
          <p class="text-gray-400">
            Enter a description to generate FreeCAD code.
          </p>
        </div>
      </div>

      <!-- Debug Panel (for testing edit mode) -->
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg shadow p-4 mb-6">
        <div class="flex justify-between items-center mb-2">
          <h3 class="text-lg font-semibold text-yellow-800">🔍 Debug Panel</h3>
          <button
            type="button"
            class="text-xs bg-yellow-500 hover:bg-yellow-600 text-white py-1 px-2 rounded transition"
            id="toggle-debug-panel"
          >
            Hide
          </button>
        </div>
        <div id="debug-panel-content" class="text-sm text-yellow-700 space-y-2">
          <div><strong>Current Session ID:</strong> <span id="debug-session-id" class="font-mono">None</span></div>
          <div><strong>Edit Mode:</strong> <span id="debug-edit-mode" class="font-mono">Off</span></div>
          <div><strong>Latest Code Available:</strong> <span id="debug-latest-code" class="font-mono">No</span></div>
          <div><strong>Last API Call:</strong> <span id="debug-last-api" class="font-mono">None</span></div>
          <div class="mt-2">
            <button
              type="button"
              class="text-xs bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded transition mr-2"
              id="debug-check-session-code"
            >
              Check Session Code
            </button>
            <button
              type="button"
              class="text-xs bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded transition mr-2"
              id="debug-force-edit-mode"
            >
              Force Edit Mode
            </button>
            <button
              type="button"
              class="text-xs bg-purple-500 hover:bg-purple-600 text-white py-1 px-2 rounded transition mr-2"
              id="debug-switch-session"
            >
              Switch Session
            </button>
            <button
              type="button"
              class="text-xs bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded transition"
              id="debug-test-api"
            >
              Test API
            </button>
          </div>
          <div class="mt-2">
            <input
              type="text"
              placeholder="Enter session ID to switch to..."
              class="text-xs border border-gray-300 rounded px-2 py-1 w-full"
              id="debug-session-input"
            />
          </div>
        </div>
      </div>

      <!-- Chat History Section -->
      <!--
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold">Chat History</h2>
          <button
            id="clear-history-btn"
            class="text-xs bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded transition"
          >
            Clear History
          </button>
        </div>
        <div
          id="chat-history"
          class="h-96 bg-gray-100 rounded p-4 overflow-y-auto text-sm text-gray-800"
        >
        </div>
      </div>

      <!-- Hidden chat messages container for compatibility -->
      <div id="chat-messages" class="hidden"></div>
    </div>

    <!-- Chatbot toggle button -->
    <div id="chat-toggle">
      <i class="fas fa-robot text-2xl"></i>
    </div>

    <!-- Chatbot widget -->
    <div
      id="chatbot-widget"
      class="bg-white rounded-lg shadow-lg overflow-hidden flex flex-col"
    >
      <!-- Chat header -->
      <div
        class="bg-indigo-600 text-white px-4 py-3 flex items-center justify-between"
      >
        <div class="flex items-center space-x-2">
          <i class="fas fa-robot"></i>
          <span class="font-medium">Text-to-CAD</span>
        </div>
        <div class="flex items-center space-x-3">
          <!-- Removed model selector -->
          <!-- Removed new chat button -->
          <button
            class="text-white hover:text-indigo-200 transition"
            id="minimize-chat"
          >
            <i class="fas fa-minus"></i>
          </button>
          <button
            class="text-white hover:text-indigo-200 transition"
            id="close-chat"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <!-- Removed chat messages section -->

      <!-- Removed saved shapes section -->
    </div>

    <script src="/static/js/main.js"></script>
  </body>
</html>
