#!/usr/bin/env python3
"""
Test script to verify the response column was added successfully.
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text
from dotenv import load_dotenv
from urllib.parse import quote_plus

# Load environment variables
load_dotenv()

def get_database_url():
    """Get database URL from environment variables."""
    db_host = os.getenv("MYSQL_HOST", "**************")
    db_port = os.getenv("MYSQL_PORT", "33061")
    db_user = os.getenv("MYSQL_USER", "root")
    db_password = os.getenv("MYSQL_ROOT_PASSWORD", os.getenv("MYSQL_PASSWORD", "root@H123"))
    db_name = os.getenv("MYSQL_DATABASE", "atn_tolery")
    
    db_password_encoded = quote_plus(db_password)
    return f"mysql+pymysql://{db_user}:{db_password_encoded}@{db_host}:{db_port}/{db_name}"

def test_response_column():
    """Test if response column exists and works."""
    database_url = get_database_url()
    
    try:
        engine = create_engine(database_url)
        
        with engine.connect() as connection:
            # Check if column exists
            check_query = text("""
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = :db_name 
                AND TABLE_NAME = 'chat_history' 
                AND COLUMN_NAME = 'response'
            """)
            
            result = connection.execute(check_query, {"db_name": os.getenv("MYSQL_DATABASE", "atn_tolery")})
            column_info = result.fetchone()
            
            if column_info:
                print("✅ Response column exists!")
                print(f"   Column Name: {column_info[0]}")
                print(f"   Data Type: {column_info[1]}")
                print(f"   Nullable: {column_info[2]}")
                print(f"   Comment: {column_info[3]}")
                
                # Test the structure of chat_history table
                structure_query = text("""
                    DESCRIBE chat_history
                """)
                
                print("\n📋 Chat History Table Structure:")
                result = connection.execute(structure_query)
                for row in result:
                    print(f"   {row[0]} | {row[1]} | {row[2]} | {row[3]} | {row[4]} | {row[5]}")
                
                return True
            else:
                print("❌ Response column does not exist!")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing response column in chat_history table...")
    print("-" * 50)
    success = test_response_column()
    print("-" * 50)
    if success:
        print("✅ Test passed! Response column is ready to use.")
    else:
        print("❌ Test failed! Please check the migration.")
