#!/usr/bin/env python3
"""
Direct database test to check response column and data.
"""

import os
from sqlalchemy import create_engine, text
from dotenv import load_dotenv
from urllib.parse import quote_plus

# Load environment variables
load_dotenv()

def get_database_url():
    """Get database URL from environment variables."""
    db_host = os.getenv("MYSQL_HOST", "**************")
    db_port = os.getenv("MYSQL_PORT", "33061")
    db_user = os.getenv("MYSQL_USER", "root")
    db_password = os.getenv("MYSQL_ROOT_PASSWORD", os.getenv("MYSQL_PASSWORD", "root@H123"))
    db_name = os.getenv("MYSQL_DATABASE", "atn_tolery")
    
    db_password_encoded = quote_plus(db_password)
    return f"mysql+pymysql://{db_user}:{db_password_encoded}@{db_host}:{db_port}/{db_name}"

def test_database():
    """Test database and response column."""
    database_url = get_database_url()
    
    try:
        engine = create_engine(database_url)
        
        with engine.connect() as connection:
            print("✅ Database connection successful!")
            
            # Check table structure
            print("\n📋 Chat History Table Structure:")
            structure_query = text("DESCRIBE chat_history")
            result = connection.execute(structure_query)
            
            has_response_column = False
            for row in result:
                print(f"   {row[0]} | {row[1]} | {row[2]}")
                if row[0] == 'response':
                    has_response_column = True
            
            if has_response_column:
                print("\n✅ Response column exists!")
            else:
                print("\n❌ Response column does NOT exist!")
                return False
            
            # Check data for specific session
            print(f"\n🔍 Checking data for session: session_2277f9_601853")
            data_query = text("""
                SELECT id, message, response, created_at 
                FROM chat_history 
                WHERE session_id = :session_id 
                ORDER BY id DESC 
                LIMIT 5
            """)
            
            result = connection.execute(data_query, {"session_id": "session_2277f9_601853"})
            rows = result.fetchall()
            
            if rows:
                print(f"Found {len(rows)} entries:")
                for row in rows:
                    print(f"   ID: {row[0]}")
                    print(f"   Message: {row[1][:50] if row[1] else 'None'}...")
                    print(f"   Response: {row[2][:50] if row[2] else 'None'}...")
                    print(f"   Created: {row[3]}")
                    print("   ---")
                return True
            else:
                print("❌ No entries found for this session")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing Database Direct Access")
    print("=" * 50)
    success = test_database()
    print("=" * 50)
    if success:
        print("✅ Database test successful!")
    else:
        print("❌ Database test failed!")
