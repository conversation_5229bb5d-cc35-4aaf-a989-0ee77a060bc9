#!/usr/bin/env python3
"""
Simple test to check if the API is working and test the response functionality.
"""

import requests
import json

# API base URL
BASE_URL = "http://localhost:8124"

def test_simple_chat():
    """Test a simple chat request."""
    print("🧪 Testing simple chat request...")
    
    payload = {
        "message": "Create a simple box 10x20x30",
        "part_file_name": "test_box",
        "material_choice": "STEEL",
        "export_format": "obj",
        "is_edit_request": False
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/chat_to_cad/",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            session_id = data.get("session_id")
            print(f"✅ Chat successful!")
            print(f"   Session ID: {session_id}")
            print(f"   Response: {data.get('chat_response', 'No response')}")
            return session_id
        else:
            print(f"❌ Chat failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_get_history(session_id):
    """Test getting chat history."""
    if not session_id:
        print("❌ No session ID to test history")
        return False
        
    print(f"\n🧪 Testing chat history for session: {session_id}")
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/sessions/{session_id}/chat",
            params={"limit": 10},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ History retrieved!")
            print(f"   Number of entries: {len(data)}")
            
            if data:
                entry = data[0]
                print(f"   Latest entry:")
                print(f"     ID: {entry.get('id')}")
                print(f"     Message: {entry.get('message', 'No message')[:50]}...")
                print(f"     Response: {entry.get('response', 'No response')[:50]}...")
                print(f"     Has response field: {'response' in entry}")
                return True
            else:
                print("   No entries found")
                return False
        else:
            print(f"❌ History failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_api_status():
    """Check if API is running."""
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API is running")
            return True
        else:
            print(f"❌ API status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API not accessible: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Simple API Test")
    print("=" * 40)
    
    # Check API status
    if not check_api_status():
        print("\n❌ API is not running. Please start it first:")
        print("   python run.py")
        exit(1)
    
    # Test chat
    session_id = test_simple_chat()
    
    # Test history
    if session_id:
        import time
        print("\n⏳ Waiting 3 seconds for database update...")
        time.sleep(3)
        success = test_get_history(session_id)
        
        print("\n" + "=" * 40)
        if success:
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed!")
    else:
        print("\n❌ Cannot test history without session ID")
