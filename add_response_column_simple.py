#!/usr/bin/env python3
"""
Simple script to add response column to database.
"""

import pymysql
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def add_response_column():
    """Add response column to chat_history table."""
    try:
        # Database connection parameters
        connection = pymysql.connect(
            host=os.getenv("MYSQL_HOST", "**************"),
            port=int(os.getenv("MYSQL_PORT", "33061")),
            user=os.getenv("MYSQL_USER", "root"),
            password=os.getenv("MYSQL_ROOT_PASSWORD", os.getenv("MYSQL_PASSWORD", "root@H123")),
            database=os.getenv("MYSQL_DATABASE", "atn_tolery"),
            charset='utf8mb4'
        )
        
        print("✅ Connected to database")
        
        with connection.cursor() as cursor:
            # Check if column already exists
            cursor.execute("""
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = %s 
                AND TABLE_NAME = 'chat_history' 
                AND COLUMN_NAME = 'response'
            """, (os.getenv("MYSQL_DATABASE", "atn_tolery"),))
            
            column_exists = cursor.fetchone()[0]
            
            if column_exists > 0:
                print("✅ Column 'response' already exists")
                return True
            
            # Add the response column
            print("🔄 Adding 'response' column...")
            cursor.execute("""
                ALTER TABLE chat_history 
                ADD COLUMN response TEXT NULL 
                COMMENT 'Store chat response from the chatbot'
            """)
            
            connection.commit()
            print("✅ Successfully added 'response' column")
            
            # Verify the column was added
            cursor.execute("DESCRIBE chat_history")
            columns = cursor.fetchall()
            
            print("\n📋 Current table structure:")
            for column in columns:
                print(f"   {column[0]} | {column[1]} | {column[2]}")
                
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    print("🚀 Adding response column to chat_history table")
    print("=" * 50)
    success = add_response_column()
    print("=" * 50)
    if success:
        print("✅ Migration completed successfully!")
    else:
        print("❌ Migration failed!")
